# EventTopia - User Manual

## 1. Getting Started

### 1.1 Accessing EventTopia
- **Live Site**: http://eventtopia.ct.ws/EventTopia/
- **Compatible Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Device Support**: Desktop, tablet, and mobile devices

### 1.2 System Requirements
- **Internet Connection**: Required for all functionality
- **JavaScript**: Must be enabled for interactive features
- **Cookies**: Must be enabled for login sessions

## 2. User Registration and Login

### 2.1 Creating a New Account
1. **Navigate to Registration**
   - Click "Register" in the top navigation bar
   - Or visit: http://eventtopia.ct.ws/EventTopia/register.php

2. **Fill Registration Form**
   - **Username**: Choose a unique username (3-50 characters)
   - **Email**: Enter a valid email address
   - **Password**: Create a secure password (minimum 6 characters)
   - **Confirm Password**: Re-enter your password
   - **First Name**: Enter your first name
   - **Last Name**: Enter your last name
   - **Phone** (Optional): Enter your phone number
   - **Address** (Optional): Enter your address

3. **Complete Registration**
   - Click "Register" button
   - Wait for confirmation message
   - You can now log in with your credentials

### 2.2 Logging In
1. **Access Login Page**
   - Click "Login" in the navigation bar
   - Or visit: http://eventtopia.ct.ws/EventTopia/login.php

2. **Enter Credentials**
   - **Email**: Enter your registered email
   - **Password**: Enter your password

3. **Login Success**
   - You'll be redirected to the homepage
   - Your username will appear in the navigation bar

### 2.3 Logging Out
- Click your username in the navigation bar
- Select "Logout" from the dropdown menu
- You'll be redirected to the homepage

## 3. Browsing and Searching Events

### 3.1 Homepage
- **Featured Events**: View the latest 6 events on the homepage
- **Quick Stats**: See total events and bookings
- **Navigation**: Use the main menu to explore different sections

### 3.2 Events Page
1. **Access Events**
   - Click "Events" in the navigation bar
   - Or visit: http://eventtopia.ct.ws/EventTopia/events.php

2. **Browse Events**
   - **Grid View**: Events displayed in a responsive grid
   - **Event Cards**: Each card shows:
     - Event title and image
     - Date, time, and venue
     - Price in XAF (Central African Franc)
     - Available tickets count
     - Category badge

3. **Search Functionality**
   - **Search Bar**: Enter keywords to search events
   - **Search Scope**: Searches in title, description, and location
   - **Real-time Results**: Results update as you type

4. **Filter by Category**
   - **Category Dropdown**: Select from available categories:
     - Technology
     - Music
     - Food & Drink
     - Business
     - Sports
     - Arts & Culture
   - **Clear Filters**: Select "All Categories" to remove filters

5. **Pagination**
   - **Page Navigation**: Use page numbers at the bottom
   - **Items per Page**: 12 events displayed per page
   - **Page Info**: Current page and total pages shown

### 3.3 Event Details
1. **View Event Details**
   - Click "View Details" on any event card
   - Or click the event title/image

2. **Event Information**
   - **Complete Description**: Full event details
   - **Event Schedule**: Date and time information
   - **Venue Details**: Location and venue information
   - **Organizer Info**: Contact details
   - **Pricing**: Ticket price in XAF
   - **Availability**: Current ticket availability

3. **Add to Cart**
   - **Quantity Selection**: Choose number of tickets (1-10)
   - **Add Button**: Click "Add to Cart"
   - **Confirmation**: Success message appears
   - **Cart Counter**: Updates in navigation bar

## 4. Shopping Cart Management

### 4.1 Viewing Cart
1. **Access Cart**
   - Click the cart icon in the navigation bar
   - Or visit: http://eventtopia.ct.ws/EventTopia/cart.php

2. **Cart Contents**
   - **Event List**: All added events displayed
   - **Event Details**: Title, date, venue, and price
   - **Quantity**: Current ticket quantity for each event
   - **Subtotal**: Price × quantity for each item
   - **Total**: Grand total for all items

### 4.2 Managing Cart Items
1. **Update Quantities**
   - **Quantity Input**: Change the number in the quantity field
   - **Update Button**: Click "Update" to save changes
   - **Auto-calculation**: Totals update automatically

2. **Remove Items**
   - **Remove Button**: Click "Remove" next to any item
   - **Confirmation**: Item is immediately removed
   - **Empty Cart**: Message shown if cart becomes empty

3. **Continue Shopping**
   - **Browse More**: Click "Continue Shopping" to add more events
   - **Returns to Events**: Redirects to events page

## 5. Checkout and Booking

### 5.1 Checkout Process
1. **Proceed to Checkout**
   - Click "Proceed to Checkout" from cart page
   - **Login Required**: Must be logged in to checkout

2. **Review Order**
   - **Order Summary**: Review all selected events
   - **Pricing Breakdown**:
     - Subtotal: Sum of all items
     - Tax (8%): Calculated automatically
     - Total: Final amount to pay

3. **Attendee Information**
   - **Attendee Name**: Full name for tickets
   - **Email**: Contact email for confirmations
   - **Phone**: Contact phone number
# EventTopia - User Manual

## 1. Getting Started

### 1.1 Accessing EventTopia
- **Live Site**: http://eventtopia.ct.ws/EventTopia/
- **Compatible Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **Device Support**: Desktop, tablet, and mobile devices

### 1.2 System Requirements
- **Internet Connection**: Required for all functionality
- **JavaScript**: Must be enabled for interactive features
- **Cookies**: Must be enabled for login sessions

## 2. User Registration and Login

### 2.1 Creating a New Account
1. **Navigate to Registration**
   - Click "Register" in the top navigation bar
   - Or visit: http://eventtopia.ct.ws/EventTopia/register.php

2. **Fill Registration Form**
   - **Username**: Choose a unique username (3-50 characters)
   - **Email**: Enter a valid email address
   - **Password**: Create a secure password (minimum 6 characters)
   - **Confirm Password**: Re-enter your password
   - **First Name**: Enter your first name
   - **Last Name**: Enter your last name
   - **Phone** (Optional): Enter your phone number
   - **Address** (Optional): Enter your address

3. **Complete Registration**
   - Click "Register" button
   - Wait for confirmation message
   - You can now log in with your credentials

### 2.2 Logging In
1. **Access Login Page**
   - Click "Login" in the navigation bar
   - Or visit: http://eventtopia.ct.ws/EventTopia/login.php

2. **Enter Credentials**
   - **Email**: Enter your registered email
   - **Password**: Enter your password

3. **Login Success**
   - You'll be redirected to the homepage
   - Your username will appear in the navigation bar

### 2.3 Logging Out
- Click your username in the navigation bar
- Select "Logout" from the dropdown menu
- You'll be redirected to the homepage

## 3. Browsing and Searching Events

### 3.1 Homepage
- **Featured Events**: View the latest 6 events on the homepage
- **Quick Stats**: See total events and bookings
- **Navigation**: Use the main menu to explore different sections

### 3.2 Events Page
1. **Access Events**
   - Click "Events" in the navigation bar
   - Or visit: http://eventtopia.ct.ws/EventTopia/events.php

2. **Browse Events**
   - **Grid View**: Events displayed in a responsive grid
   - **Event Cards**: Each card shows:
     - Event title and image
     - Date, time, and venue
     - Price in XAF (Central African Franc)
     - Available tickets count
     - Category badge

3. **Search Functionality**
   - **Search Bar**: Enter keywords to search events
   - **Search Scope**: Searches in title, description, and location
   - **Real-time Results**: Results update as you type

4. **Filter by Category**
   - **Category Dropdown**: Select from available categories:
     - Technology
     - Music
     - Food & Drink
     - Business
     - Sports
     - Arts & Culture
   - **Clear Filters**: Select "All Categories" to remove filters

5. **Pagination**
   - **Page Navigation**: Use page numbers at the bottom
   - **Items per Page**: 12 events displayed per page
   - **Page Info**: Current page and total pages shown

### 3.3 Event Details
1. **View Event Details**
   - Click "View Details" on any event card
   - Or click the event title/image

2. **Event Information**
   - **Complete Description**: Full event details
   - **Event Schedule**: Date and time information
   - **Venue Details**: Location and venue information
   - **Organizer Info**: Contact details
   - **Pricing**: Ticket price in XAF
   - **Availability**: Current ticket availability

3. **Add to Cart**
   - **Quantity Selection**: Choose number of tickets (1-10)
   - **Add Button**: Click "Add to Cart"
   - **Confirmation**: Success message appears
   - **Cart Counter**: Updates in navigation bar

## 4. Shopping Cart Management

### 4.1 Viewing Cart
1. **Access Cart**
   - Click the cart icon in the navigation bar
   - Or visit: http://eventtopia.ct.ws/EventTopia/cart.php

2. **Cart Contents**
   - **Event List**: All added events displayed
   - **Event Details**: Title, date, venue, and price
   - **Quantity**: Current ticket quantity for each event
   - **Subtotal**: Price × quantity for each item
   - **Total**: Grand total for all items

### 4.2 Managing Cart Items
1. **Update Quantities**
   - **Quantity Input**: Change the number in the quantity field
   - **Update Button**: Click "Update" to save changes
   - **Auto-calculation**: Totals update automatically

2. **Remove Items**
   - **Remove Button**: Click "Remove" next to any item
   - **Confirmation**: Item is immediately removed
   - **Empty Cart**: Message shown if cart becomes empty

3. **Continue Shopping**
   - **Browse More**: Click "Continue Shopping" to add more events
   - **Returns to Events**: Redirects to events page

## 5. Checkout and Booking

### 5.1 Checkout Process
1. **Proceed to Checkout**
   - Click "Proceed to Checkout" from cart page
   - **Login Required**: Must be logged in to checkout

2. **Review Order**
   - **Order Summary**: Review all selected events
   - **Pricing Breakdown**:
     - Subtotal: Sum of all items
     - Tax (8%): Calculated automatically
     - Total: Final amount to pay

3. **Attendee Information**
   - **Attendee Name**: Full name for tickets
   - **Email**: Contact email for confirmations
   - **Phone**: Contact phone number

4. **Complete Booking**
   - **Submit Order**: Click "Complete Booking"
   - **Processing**: System processes the booking
   - **Confirmation**: Redirect to booking history

### 5.2 Booking Confirmation
- **Booking Reference**: Unique reference number generated
- **Status**: Booking marked as "Confirmed"
- **Payment**: Marked as "Completed" (simulation)
- **Tickets**: Available for download

## 6. User Dashboard

### 6.1 Accessing Dashboard
- Click your username in navigation
- Select "Dashboard" from dropdown menu
- Or visit: http://eventtopia.ct.ws/EventTopia/user/dashboard.php

### 6.2 Dashboard Features
1. **Quick Stats**
   - Total bookings count
   - Total amount spent
   - Upcoming events count

2. **Recent Bookings**
   - Last 5 bookings displayed
   - Quick access to booking details

3. **Quick Actions**
   - View all bookings
   - Update profile
   - Browse new events

## 7. Booking History

### 7.1 View Booking History
1. **Access Booking History**
   - From dashboard, click "View All Bookings"
   - Or visit: http://eventtopia.ct.ws/EventTopia/user/booking_history.php

2. **Booking Information**
   - **Booking Reference**: Unique identifier
   - **Event Details**: Title, date, venue
   - **Quantity**: Number of tickets booked
   - **Total Amount**: Amount paid in XAF
   - **Status**: Booking status (Confirmed/Pending/Cancelled)
   - **Booking Date**: When booking was made

3. **Download Tickets**
   - **Download Button**: Click "Download Ticket" for each booking
   - **PDF Format**: Tickets generated as PDF files
   - **Ticket Details**: Includes all event and booking information

## 8. Profile Management

### 8.1 Update Profile
1. **Access Profile**
   - Click username in navigation
   - Select "Profile" from dropdown
   - Or visit: http://eventtopia.ct.ws/EventTopia/user/profile.php

2. **Editable Information**
   - First name and last name
   - Phone number
   - Address
   - **Note**: Email and username cannot be changed

3. **Save Changes**
   - Click "Update Profile" to save
   - Confirmation message appears

## 9. Mobile Usage

### 9.1 Mobile Features
- **Responsive Design**: Optimized for mobile devices
- **Touch-friendly**: Large buttons and easy navigation
- **Mobile Menu**: Collapsible navigation menu
- **Fast Loading**: Optimized images and code

### 9.2 Mobile-specific Tips
- **Zoom**: Pinch to zoom on event images
- **Scrolling**: Smooth scrolling on all pages
- **Forms**: Mobile-optimized form inputs
- **Cart**: Easy cart management on small screens

## 10. Troubleshooting

### 10.1 Common Issues

1. **Login Problems**
   - **Forgot Password**: Contact administrator
   - **Account Locked**: Try again after a few minutes
   - **Invalid Credentials**: Check email and password

2. **Cart Issues**
   - **Items Not Adding**: Ensure you're logged in
   - **Quantity Errors**: Check ticket availability
   - **Cart Empty**: Items may have expired

3. **Booking Problems**
   - **Checkout Fails**: Check all required fields
   - **Payment Issues**: Contact support
   - **Tickets Unavailable**: Event may be sold out

4. **Technical Issues**
   - **Page Not Loading**: Refresh the browser
   - **JavaScript Errors**: Enable JavaScript
   - **Slow Performance**: Check internet connection

### 10.2 Browser Compatibility
- **Recommended**: Use latest browser versions
- **JavaScript**: Must be enabled
- **Cookies**: Required for login sessions
- **Pop-ups**: Allow for ticket downloads

### 10.3 Getting Help
- **Contact Information**: <EMAIL>
- **Response Time**: 24-48 hours
- **Support Hours**: Monday-Friday, 9 AM - 5 PM

## 11. Tips for Best Experience

### 11.1 General Tips
- **Keep Login Active**: Stay logged in while browsing
- **Check Availability**: Popular events sell out quickly
- **Mobile Friendly**: Use on any device
- **Bookmark**: Save the site for easy access

### 11.2 Booking Tips
- **Book Early**: Secure tickets for popular events
- **Check Details**: Verify event information before booking
- **Save Tickets**: Download and save ticket PDFs
- **Contact Info**: Keep contact information updated

This user manual provides comprehensive guidance for using all features of the EventTopia platform, ensuring users can effectively browse, book, and manage their event experiences.

4. **Complete Booking**
   - **Submit Order**: Click "Complete Booking"
   - **Processing**: System processes the booking
   - **Confirmation**: Redirect to booking history

### 5.2 Booking Confirmation
- **Booking Reference**: Unique reference number generated
- **Status**: Booking marked as "Confirmed"
- **Payment**: Marked as "Completed" (simulation)
- **Tickets**: Available for download

## 6. User Dashboard

### 6.1 Accessing Dashboard
- Click your username in navigation
- Select "Dashboard" from dropdown menu
- Or visit: http://eventtopia.ct.ws/EventTopia/user/dashboard.php

### 6.2 Dashboard Features
1. **Quick Stats**
   - Total bookings count
   - Total amount spent
   - Upcoming events count

2. **Recent Bookings**
   - Last 5 bookings displayed
   - Quick access to booking details

3. **Quick Actions**
   - View all bookings
   - Update profile
   - Browse new events

## 7. Booking History

### 7.1 View Booking History
1. **Access Booking History**
   - From dashboard, click "View All Bookings"
   - Or visit: http://eventtopia.ct.ws/EventTopia/user/booking_history.php

2. **Booking Information**
   - **Booking Reference**: Unique identifier
   - **Event Details**: Title, date, venue
   - **Quantity**: Number of tickets booked
   - **Total Amount**: Amount paid in XAF
   - **Status**: Booking status (Confirmed/Pending/Cancelled)
   - **Booking Date**: When booking was made

3. **Download Tickets**
   - **Download Button**: Click "Download Ticket" for each booking
   - **PDF Format**: Tickets generated as PDF files
   - **Ticket Details**: Includes all event and booking information

## 8. Profile Management

### 8.1 Update Profile
1. **Access Profile**
   - Click username in navigation
   - Select "Profile" from dropdown
   - Or visit: http://eventtopia.ct.ws/EventTopia/user/profile.php

2. **Editable Information**
   - First name and last name
   - Phone number
   - Address
   - **Note**: Email and username cannot be changed

3. **Save Changes**
   - Click "Update Profile" to save
   - Confirmation message appears

## 9. Mobile Usage

### 9.1 Mobile Features
- **Responsive Design**: Optimized for mobile devices
- **Touch-friendly**: Large buttons and easy navigation
- **Mobile Menu**: Collapsible navigation menu
- **Fast Loading**: Optimized images and code

### 9.2 Mobile-specific Tips
- **Zoom**: Pinch to zoom on event images
- **Scrolling**: Smooth scrolling on all pages
- **Forms**: Mobile-optimized form inputs
- **Cart**: Easy cart management on small screens

## 10. Troubleshooting

### 10.1 Common Issues

1. **Login Problems**
   - **Forgot Password**: Contact administrator
   - **Account Locked**: Try again after a few minutes
   - **Invalid Credentials**: Check email and password

2. **Cart Issues**
   - **Items Not Adding**: Ensure you're logged in
   - **Quantity Errors**: Check ticket availability
   - **Cart Empty**: Items may have expired

3. **Booking Problems**
   - **Checkout Fails**: Check all required fields
   - **Payment Issues**: Contact support
   - **Tickets Unavailable**: Event may be sold out

4. **Technical Issues**
   - **Page Not Loading**: Refresh the browser
   - **JavaScript Errors**: Enable JavaScript
   - **Slow Performance**: Check internet connection

### 10.2 Browser Compatibility
- **Recommended**: Use latest browser versions
- **JavaScript**: Must be enabled
- **Cookies**: Required for login sessions
- **Pop-ups**: Allow for ticket downloads

### 10.3 Getting Help
- **Contact Information**: <EMAIL>
- **Response Time**: 24-48 hours
- **Support Hours**: Monday-Friday, 9 AM - 5 PM

## 11. Tips for Best Experience

### 11.1 General Tips
- **Keep Login Active**: Stay logged in while browsing
- **Check Availability**: Popular events sell out quickly
- **Mobile Friendly**: Use on any device
- **Bookmark**: Save the site for easy access

### 11.2 Booking Tips
- **Book Early**: Secure tickets for popular events
- **Check Details**: Verify event information before booking
- **Save Tickets**: Download and save ticket PDFs
- **Contact Info**: Keep contact information updated

This user manual provides comprehensive guidance for using all features of the EventTopia platform, ensuring users can effectively browse, book, and manage their event experiences.
