# EventTopia - Deployment Documentation

## 1. Deployment Overview

EventTopia has been successfully deployed on InfinityFree hosting platform, providing a live demonstration of the event booking system. This document covers both the production deployment and local development setup procedures.

### 1.1 Live Deployment Information
- **Production URL**: http://eventtopia.ct.ws/EventTopia/
- **Hosting Provider**: InfinityFree
- **Server Environment**: Linux/Apache/MySQL/PHP (LAMP)
- **Database**: MySQL 8.0
- **PHP Version**: 8.0+

## 2. Production Deployment (InfinityFree)

### 2.1 Account Setup
1. **Create InfinityFree Account**
   - Visit https://infinityfree.net/
   - Sign up for a free hosting account
   - Choose subdomain: eventtopia.ct.ws

2. **Control Panel Access**
   - Access VistaPanel control panel
   - Note down FTP credentials
   - Access MySQL database details

### 2.2 File Upload Process
```bash
# Using FTP client (FileZilla recommended)
Host: ftpupload.net
Username: [provided by InfinityFree]
Password: [provided by InfinityFree]
Port: 21

# Upload directory structure:
/htdocs/EventTopia/
├── config/
├── includes/
├── assets/
├── ajax/
├── admin/
├── user/
├── *.php files
└── database.sql
```

### 2.3 Database Configuration
1. **Create MySQL Database**
   - Database Name: `epiz_34567890_eventtopia`
   - Username: `epiz_34567890`
   - Password: [generated by system]
   - Host: `sql200.infinityfree.com`

2. **Import Database Schema**
   ```sql
   -- Access phpMyAdmin through control panel
   -- Import database.sql file
   -- Verify all tables are created successfully
   ```

3. **Update Configuration Files**
   ```php
   // config/database.php - Production settings
   private $host = 'sql200.infinityfree.com';
   private $db_name = 'epiz_34567890_eventtopia';
   private $username = 'epiz_34567890';
   private $password = 'production_password';
   ```

### 2.4 File Permissions
```bash
# Set appropriate permissions for web server
chmod 755 /htdocs/EventTopia/
chmod 644 /htdocs/EventTopia/*.php
chmod 755 /htdocs/EventTopia/assets/images/
chmod 777 /htdocs/EventTopia/assets/images/events/
```

### 2.5 Production Configuration Updates
```php
// config/config.php - Production settings
define('SITE_URL', 'http://eventtopia.ct.ws/EventTopia');
define('UPLOAD_PATH', 'assets/images/events/');

// Error reporting for production
error_reporting(0);
ini_set('display_errors', 0);
```

## 3. Local Development Setup

### 3.1 LAMP Stack Installation (Ubuntu/Debian)
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Apache web server
sudo apt install apache2 -y
sudo systemctl start apache2
sudo systemctl enable apache2

# Install MySQL database server
sudo apt install mysql-server -y
sudo mysql_secure_installation

# Install PHP and required extensions
sudo apt install php php-mysql php-cli php-curl php-json php-mbstring php-gd -y

# Enable Apache modules
sudo a2enmod rewrite
sudo systemctl restart apache2
```

### 3.2 Project Setup
```bash
# Clone or download project files
cd /var/www/html/
sudo mkdir EventTopia
sudo chown $USER:$USER EventTopia/

# Copy project files to web directory
cp -r /path/to/project/* /var/www/html/EventTopia/

# Set proper permissions
sudo chown -R www-data:www-data /var/www/html/EventTopia/
sudo chmod -R 755 /var/www/html/EventTopia/
sudo chmod -R 777 /var/www/html/EventTopia/assets/images/
```

### 3.3 Database Setup
```bash
# Access MySQL as root
sudo mysql -u root -p

# Create database and user
CREATE DATABASE eventtopia;
CREATE USER 'eventtopia_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON eventtopia.* TO 'eventtopia_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Import database schema
mysql -u eventtopia_user -p eventtopia < /var/www/html/EventTopia/database.sql
```

### 3.4 Apache Virtual Host Configuration
```apache
# Create virtual host file
sudo nano /etc/apache2/sites-available/eventtopia.conf

<VirtualHost *:80>
    ServerName eventtopia.local
    DocumentRoot /var/www/html/EventTopia
    
    <Directory /var/www/html/EventTopia>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/eventtopia_error.log
    CustomLog ${APACHE_LOG_DIR}/eventtopia_access.log combined
</VirtualHost>

# Enable site and restart Apache
sudo a2ensite eventtopia.conf
sudo systemctl restart apache2

# Add to hosts file
echo "127.0.0.1 eventtopia.local" | sudo tee -a /etc/hosts
```

## 4. Environment Configuration

### 4.1 Development Environment
```php
// config/config.php - Development settings
define('SITE_URL', 'http://eventtopia.local');
define('SITE_EMAIL', '<EMAIL>');

// Debug settings
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', '/var/log/php_errors.log');
```

### 4.2 Production Environment
```php
// config/config.php - Production settings
define('SITE_URL', 'http://eventtopia.ct.ws/EventTopia');
define('SITE_EMAIL', '<EMAIL>');

// Security settings
error_reporting(0);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
```

## 5. SSL/HTTPS Configuration (Optional)

### 5.1 Let's Encrypt SSL (For VPS/Dedicated hosting)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-apache -y

# Obtain SSL certificate
sudo certbot --apache -d eventtopia.yourdomain.com

# Auto-renewal setup
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 5.2 Force HTTPS Redirect
```apache
# .htaccess file
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## 6. Backup and Maintenance

### 6.1 Database Backup
```bash
# Create backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u eventtopia_user -p eventtopia > backup_$DATE.sql
gzip backup_$DATE.sql

# Schedule daily backups
crontab -e
# Add: 0 2 * * * /path/to/backup_script.sh
```

### 6.2 File Backup
```bash
# Create file backup
tar -czf eventtopia_files_$(date +%Y%m%d).tar.gz /var/www/html/EventTopia/

# Exclude unnecessary files
tar --exclude='*.log' --exclude='cache/*' -czf backup.tar.gz /var/www/html/EventTopia/
```

## 7. Monitoring and Logging

### 7.1 Error Logging
```php
// Enable error logging
ini_set('log_errors', 1);
ini_set('error_log', '/var/log/eventtopia_errors.log');

// Custom error handler
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    $error_message = date('Y-m-d H:i:s') . " - Error: $errstr in $errfile on line $errline\n";
    error_log($error_message, 3, '/var/log/eventtopia_errors.log');
}
set_error_handler('customErrorHandler');
```

### 7.2 Access Monitoring
```bash
# Monitor Apache access logs
tail -f /var/log/apache2/access.log | grep EventTopia

# Monitor error logs
tail -f /var/log/apache2/error.log
```

## 8. Performance Optimization

### 8.1 Apache Optimization
```apache
# Enable compression
LoadModule deflate_module modules/mod_deflate.so
<Location />
    SetOutputFilter DEFLATE
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png)$ no-gzip dont-vary
</Location>

# Enable caching
LoadModule expires_module modules/mod_expires.so
ExpiresActive On
ExpiresByType text/css "access plus 1 month"
ExpiresByType application/javascript "access plus 1 month"
ExpiresByType image/png "access plus 1 year"
```

### 8.2 PHP Optimization
```ini
# php.ini optimizations
memory_limit = 256M
max_execution_time = 30
upload_max_filesize = 10M
post_max_size = 10M
opcache.enable = 1
opcache.memory_consumption = 128
```

## 9. Security Hardening

### 9.1 File Security
```bash
# Remove sensitive files from web directory
rm -f /var/www/html/EventTopia/.git
rm -f /var/www/html/EventTopia/database.sql
rm -f /var/www/html/EventTopia/README.md
```

### 9.2 Apache Security
```apache
# .htaccess security rules
# Deny access to sensitive files
<Files "config.php">
    Require all denied
</Files>

<Files "database.php">
    Require all denied
</Files>

# Prevent directory browsing
Options -Indexes

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

## 10. Troubleshooting

### 10.1 Common Issues
1. **Database Connection Error**
   - Verify database credentials
   - Check MySQL service status
   - Confirm database exists

2. **File Permission Issues**
   - Check web server user permissions
   - Verify upload directory permissions
   - Ensure proper ownership

3. **Image Upload Problems**
   - Check upload directory exists
   - Verify file size limits
   - Confirm file type restrictions

### 10.2 Debug Mode
```php
// Enable debug mode for troubleshooting
define('DEBUG_MODE', true);

if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    // Database debug
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
}
```

This deployment documentation provides comprehensive guidance for both production deployment on InfinityFree and local development setup, ensuring successful installation and configuration of the EventTopia platform.
