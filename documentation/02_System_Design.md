# EventTopia - System Design & UML Diagrams

## 1. System Architecture Overview

EventTopia follows a layered architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────────┐
│           Presentation Layer            │
│  (HTML, CSS, JavaScript, Bootstrap)     │
├─────────────────────────────────────────┤
│           Application Layer             │
│     (PHP Controllers & Business Logic)  │
├─────────────────────────────────────────┤
│            Data Access Layer           │
│         (PDO Database Operations)       │
├─────────────────────────────────────────┤
│             Database Layer              │
│              (MySQL Database)           │
└─────────────────────────────────────────┘
```

## 2. Database Design

### 2.1 Entity Relationship Diagram (ERD)

The database consists of four main entities with the following relationships:

- **Users** (1:N) **Bookings**: One user can have multiple bookings
- **Events** (1:N) **Bookings**: One event can have multiple bookings
- **Users** (1:N) **Cart**: One user can have multiple cart items
- **Events** (1:N) **Cart**: One event can be in multiple carts

### 2.2 Database Schema

#### Users Table
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON>HA<PERSON>(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### Events Table
```sql
CREATE TABLE events (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    event_date DATE NOT NULL,
    event_time TIME NOT NULL,
    venue VARCHAR(200) NOT NULL,
    location VARCHAR(200) NOT NULL,
    organizer VARCHAR(100) NOT NULL,
    organizer_contact VARCHAR(100),
    image VARCHAR(255),
    price DECIMAL(10,2) NOT NULL,
    available_tickets INT NOT NULL,
    total_tickets INT NOT NULL,
    category VARCHAR(50),
    status ENUM('active', 'inactive', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### Bookings Table
```sql
CREATE TABLE bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    booking_reference VARCHAR(20) UNIQUE NOT NULL,
    attendee_name VARCHAR(100) NOT NULL,
    attendee_email VARCHAR(100) NOT NULL,
    attendee_phone VARCHAR(20),
    status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    booking_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);
```

#### Cart Table
```sql
CREATE TABLE cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_id INT NOT NULL,
    quantity INT NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_event (user_id, event_id)
);
```

## 3. Use Case Diagrams

### 3.1 User Use Cases

**Primary Actors**: User, Admin

**User Use Cases**:
- Register Account
- Login/Logout
- Browse Events
- Search Events
- View Event Details
- Add to Cart
- Manage Cart
- Checkout
- View Booking History
- Download Tickets
- Update Profile

**Admin Use Cases**:
- Admin Login
- Manage Events (CRUD)
- View Bookings
- Manage Users
- Generate Reports
- View Dashboard Statistics

### 3.2 System Use Case Description

#### Use Case: Book Event Tickets
- **Actor**: Registered User
- **Precondition**: User is logged in
- **Main Flow**:
  1. User browses events
  2. User selects event
  3. User adds event to cart
  4. User proceeds to checkout
  5. User fills booking details
  6. System processes booking
  7. System confirms booking
  8. User receives booking confirmation

## 4. Class Diagrams

### 4.1 Core Classes Structure

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│      User       │    │      Event      │    │     Booking     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ - id            │    │ - id            │    │ - id            │
│ - username      │    │ - title         │    │ - user_id       │
│ - email         │    │ - description   │    │ - event_id      │
│ - password      │    │ - event_date    │    │ - quantity      │
│ - first_name    │    │ - event_time    │    │ - total_amount  │
│ - last_name     │    │ - venue         │    │ - booking_ref   │
│ - phone         │    │ - location      │    │ - status        │
│ - role          │    │ - price         │    │ - booking_date  │
├─────────────────┤    │ - tickets       │    ├─────────────────┤
│ + register()    │    │ - category      │    │ + create()      │
│ + login()       │    │ - status        │    │ + cancel()      │
│ + updateProfile()│   ├─────────────────┤    │ + getDetails()  │
└─────────────────┘    │ + create()      │    └─────────────────┘
                       │ + update()      │
                       │ + delete()      │
                       │ + getActive()   │
                       └─────────────────┘
```

### 4.2 Cart Management

```
┌─────────────────┐
│      Cart       │
├─────────────────┤
│ - id            │
│ - user_id       │
│ - event_id      │
│ - quantity      │
│ - added_at      │
├─────────────────┤
│ + addItem()     │
│ + updateItem()  │
│ + removeItem()  │
│ + getItems()    │
│ + clearCart()   │
└─────────────────┘
```

## 5. Sequence Diagrams

### 5.1 User Registration Sequence

```
User → Registration Page → Validation → Database → Confirmation
  |         |                |           |           |
  |    Fill Form         Validate     Insert      Send Email
  |         |             Input       User         (Future)
  |    Submit Form          |           |           |
  |         |          Return Result    |           |
  |    Show Result          |           |           |
```

### 5.2 Event Booking Sequence

```
User → Event Page → Cart → Checkout → Payment → Booking → Confirmation
  |       |         |        |         |         |         |
  |   View Event  Add to   Fill Form  Process  Create    Send Email
  |       |       Cart       |       Payment  Booking   (Future)
  |   Select Qty    |    Submit Form    |         |         |
  |       |    Update Cart     |        |    Update       |
  |   Add to Cart    |         |        |    Tickets      |
```

## 6. Activity Diagrams

### 6.1 Event Booking Process

```
[Start] → [Browse Events] → [Select Event] → [Add to Cart]
                                                    ↓
[Checkout] ← [Review Cart] ← [Continue Shopping?] ←─┘
    ↓
[Fill Details] → [Validate] → [Process Booking] → [Confirmation] → [End]
                     ↓
                [Show Errors] ← [Invalid?]
```

### 6.2 Admin Event Management

```
[Start] → [Login] → [Admin Dashboard] → [Manage Events]
                                            ↓
[Create Event] ← [Select Action] → [Edit Event]
     ↓                                  ↓
[Fill Form]                        [Update Form]
     ↓                                  ↓
[Save Event] → [Validation] → [Success] → [Event List] → [End]
                    ↓
               [Show Errors]
```

## 7. Component Diagram

### 7.1 System Components

```
┌─────────────────────────────────────────────────────────────┐
│                    EventTopia System                        │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Frontend  │  │   Backend   │  │      Database       │  │
│  │ Components  │  │ Components  │  │     Components      │  │
│  ├─────────────┤  ├─────────────┤  ├─────────────────────┤  │
│  │ - HTML/CSS  │  │ - Auth      │  │ - Users Table       │  │
│  │ - JavaScript│  │ - Events    │  │ - Events Table      │  │
│  │ - Bootstrap │  │ - Bookings  │  │ - Bookings Table    │  │
│  │ - jQuery    │  │ - Cart      │  │ - Cart Table        │  │
│  │ - AJAX      │  │ - Reports   │  │ - Indexes           │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 8. Deployment Diagram

### 8.1 Production Environment

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   Web Server    │    │   Database      │
│                 │    │   (Apache)      │    │   Server        │
│ - HTML/CSS/JS   │◄──►│ - PHP Engine    │◄──►│ - MySQL         │
│ - User Interface│    │ - EventTopia    │    │ - Data Storage  │
└─────────────────┘    │   Application   │    └─────────────────┘
                       └─────────────────┘
```

### 8.2 File Structure Organization

```
EventTopia/
├── config/              # Configuration files
├── includes/            # Common includes
├── assets/              # Static assets
│   ├── css/            # Stylesheets
│   ├── js/             # JavaScript files
│   └── images/         # Image files
├── ajax/               # AJAX endpoints
├── admin/              # Admin panel
├── user/               # User dashboard
├── documentation/      # Project documentation
└── *.php              # Main application files
```

This design document provides a comprehensive view of EventTopia's architecture, database design, and system interactions through various UML diagrams and technical specifications.
