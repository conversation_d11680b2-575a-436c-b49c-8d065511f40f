# EventTopia - Code Explanation & Architecture

## 1. Application Architecture

EventTopia follows a modular, MVC-inspired architecture with clear separation of concerns:

### 1.1 Directory Structure Explanation
```
EventTopia/
├── config/              # Configuration and database connection
├── includes/            # Reusable components (header, footer, navbar)
├── assets/              # Static files (CSS, JS, images)
├── ajax/                # AJAX endpoints for dynamic functionality
├── admin/               # Administrative interface
├── user/                # User dashboard and profile management
├── *.php               # Main application pages
└── documentation/       # Project documentation
```

## 2. Core Configuration Files

### 2.1 Database Connection (config/database.php)
```php
class Database {
    private $host = 'localhost';
    private $db_name = 'eventtopia';
    private $username = 'eventtopia_user';
    private $password = 'secure_password';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        return $this->conn;
    }
}
```

**Key Features:**
- **PDO Implementation**: Uses PHP Data Objects for database abstraction
- **Error Handling**: Comprehensive exception handling for connection issues
- **Security**: Prepared statements prevent SQL injection attacks
- **Singleton Pattern**: Single database connection throughout the application

### 2.2 Application Configuration (config/config.php)
```php
// Site settings
define('SITE_NAME', 'EventTopia');
define('SITE_URL', 'http://localhost');
define('UPLOAD_PATH', 'assets/images/events/');
define('EVENTS_PER_PAGE', 12);

// Helper functions
function redirect($url) {
    echo "<script>window.location.href = '" . addslashes($url) . "';</script>";
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function requireAdmin() {
    if (!isLoggedIn() || !isAdmin()) {
        redirect('adminlogin.php');
    }
}
```

**Key Features:**
- **Global Constants**: Centralized configuration management
- **Helper Functions**: Reusable utility functions
- **Security Functions**: Authentication and authorization helpers
- **Session Management**: User state management

## 3. Authentication System

### 3.1 User Registration (register.php)
```php
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitizeInput($_POST['username']);
    $email = sanitizeInput($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validation
    if (empty($username) || empty($email) || empty($password)) {
        $error = "All fields are required.";
    } elseif ($password !== $confirm_password) {
        $error = "Passwords do not match.";
    } else {
        // Check if user exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
        $stmt->execute([$email, $username]);
        
        if ($stmt->fetch()) {
            $error = "User already exists with this email or username.";
        } else {
            // Create user
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO users (username, email, password, first_name, last_name) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            if ($stmt->execute([$username, $email, $hashed_password, $first_name, $last_name])) {
                $success = "Registration successful! Please login.";
            }
        }
    }
}
```

**Key Features:**
- **Input Sanitization**: Prevents XSS attacks
- **Password Hashing**: Secure password storage using PHP's password_hash()
- **Duplicate Prevention**: Checks for existing users
- **Validation**: Server-side input validation

### 3.2 User Login (login.php)
```php
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = sanitizeInput($_POST['email']);
    $password = $_POST['password'];
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        
        if ($user['role'] === 'admin') {
            redirect('admin/index.php');
        } else {
            redirect('index.php');
        }
    } else {
        $error = "Invalid email or password.";
    }
}
```

**Key Features:**
- **Password Verification**: Secure password checking
- **Session Management**: User state persistence
- **Role-based Routing**: Different redirects for users and admins
- **Error Handling**: User-friendly error messages

## 4. Event Management System

### 4.1 Event Display (events.php)
```php
// Pagination setup
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * EVENTS_PER_PAGE;

// Search and filter functionality
$search = isset($_GET['search']) ? sanitizeInput($_GET['search']) : '';
$category = isset($_GET['category']) ? sanitizeInput($_GET['category']) : '';

// Build query with filters
$where_conditions = ["status = 'active'", "event_date >= CURDATE()"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(title LIKE ? OR description LIKE ? OR location LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

if (!empty($category)) {
    $where_conditions[] = "category = ?";
    $params[] = $category;
}

$where_clause = implode(' AND ', $where_conditions);
$sql = "SELECT * FROM events WHERE $where_clause ORDER BY event_date ASC LIMIT " . EVENTS_PER_PAGE . " OFFSET $offset";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$events = $stmt->fetchAll();
```

**Key Features:**
- **Pagination**: Efficient handling of large datasets
- **Search Functionality**: Multi-field search capability
- **Category Filtering**: Event categorization
- **Dynamic Query Building**: Flexible SQL generation
- **Parameter Binding**: SQL injection prevention

### 4.2 Event Creation (admin/add_event.php)
```php
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // File upload handling
    $image_name = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $file_type = $_FILES['image']['type'];
        
        if (in_array($file_type, $allowed_types)) {
            $image_name = time() . '_' . $_FILES['image']['name'];
            $upload_path = '../' . UPLOAD_PATH . $image_name;
            
            if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                $error = "Failed to upload image.";
            }
        } else {
            $error = "Invalid file type. Only JPG, PNG, and GIF allowed.";
        }
    }
    
    if (empty($error)) {
        $stmt = $pdo->prepare("
            INSERT INTO events (title, description, event_date, event_time, venue, 
                              location, organizer, organizer_contact, price, 
                              available_tickets, total_tickets, category, image) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        if ($stmt->execute([$title, $description, $event_date, $event_time, $venue, 
                           $location, $organizer, $organizer_contact, $price, 
                           $available_tickets, $total_tickets, $category, $image_name])) {
            $success = "Event created successfully!";
        }
    }
}
```

**Key Features:**
- **File Upload Security**: Type validation and secure file handling
- **Input Validation**: Comprehensive form validation
- **Unique Naming**: Timestamp-based file naming to prevent conflicts
- **Error Handling**: Detailed error reporting

## 5. Shopping Cart System

### 5.1 AJAX Cart Management (ajax/add_to_cart.php)
```php
header('Content-Type: application/json');

if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $event_id = (int)$_POST['event_id'];
    $quantity = (int)$_POST['quantity'];
    $user_id = $_SESSION['user_id'];
    
    // Validate event exists and has available tickets
    $stmt = $pdo->prepare("SELECT available_tickets FROM events WHERE id = ? AND status = 'active'");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch();
    
    if (!$event) {
        echo json_encode(['success' => false, 'message' => 'Event not found']);
        exit;
    }
    
    if ($event['available_tickets'] < $quantity) {
        echo json_encode(['success' => false, 'message' => 'Not enough tickets available']);
        exit;
    }
    
    // Check if item already in cart
    $stmt = $pdo->prepare("SELECT quantity FROM cart WHERE user_id = ? AND event_id = ?");
    $stmt->execute([$user_id, $event_id]);
    $existing = $stmt->fetch();
    
    if ($existing) {
        // Update quantity
        $new_quantity = $existing['quantity'] + $quantity;
        $stmt = $pdo->prepare("UPDATE cart SET quantity = ? WHERE user_id = ? AND event_id = ?");
        $stmt->execute([$new_quantity, $user_id, $event_id]);
    } else {
        // Insert new item
        $stmt = $pdo->prepare("INSERT INTO cart (user_id, event_id, quantity) VALUES (?, ?, ?)");
        $stmt->execute([$user_id, $event_id, $quantity]);
    }
    
    echo json_encode(['success' => true, 'message' => 'Item added to cart']);
}
```

**Key Features:**
- **AJAX Response**: JSON-based communication
- **Authentication Check**: Ensures user is logged in
- **Inventory Validation**: Checks ticket availability
- **Duplicate Handling**: Updates existing cart items
- **Error Responses**: Detailed error messaging

### 5.2 Cart Display (cart.php)
```php
$stmt = $pdo->prepare("
    SELECT c.*, e.title, e.event_date, e.event_time, e.venue, e.location, e.price, e.available_tickets
    FROM cart c
    JOIN events e ON c.event_id = e.id
    WHERE c.user_id = ? AND e.status = 'active'
    ORDER BY c.added_at DESC
");
$stmt->execute([$_SESSION['user_id']]);
$cart_items = $stmt->fetchAll();

// Calculate totals
$subtotal = 0;
foreach ($cart_items as $item) {
    $subtotal += $item['price'] * $item['quantity'];
}
```

**Key Features:**
- **JOIN Queries**: Efficient data retrieval
- **Real-time Calculation**: Dynamic total computation
- **Data Validation**: Ensures active events only

## 6. Booking System

### 6.1 Checkout Process (checkout.php)
```php
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $attendee_name = sanitizeInput($_POST['attendee_name']);
    $attendee_email = sanitizeInput($_POST['attendee_email']);
    $attendee_phone = sanitizeInput($_POST['attendee_phone']);
    
    try {
        $pdo->beginTransaction();
        
        foreach ($cart_items as $item) {
            // Verify ticket availability
            $stmt = $pdo->prepare("SELECT available_tickets FROM events WHERE id = ? FOR UPDATE");
            $stmt->execute([$item['event_id']]);
            $available = $stmt->fetchColumn();
            
            if ($available < $item['quantity']) {
                throw new Exception("Not enough tickets available for " . $item['title']);
            }
            
            // Create booking
            $booking_reference = generateBookingReference();
            $booking_total = $item['price'] * $item['quantity'];
            
            $stmt = $pdo->prepare("
                INSERT INTO bookings (user_id, event_id, quantity, total_amount, 
                                    booking_reference, attendee_name, attendee_email, 
                                    attendee_phone, status, payment_status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'confirmed', 'completed')
            ");
            
            $stmt->execute([$_SESSION['user_id'], $item['event_id'], $item['quantity'], 
                           $booking_total, $booking_reference, $attendee_name, 
                           $attendee_email, $attendee_phone]);
            
            // Update available tickets
            $stmt = $pdo->prepare("UPDATE events SET available_tickets = available_tickets - ? WHERE id = ?");
            $stmt->execute([$item['quantity'], $item['event_id']]);
        }
        
        // Clear cart
        $stmt = $pdo->prepare("DELETE FROM cart WHERE user_id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        
        $pdo->commit();
        redirect('user/booking_history.php');
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = $e->getMessage();
    }
}
```

**Key Features:**
- **Transaction Management**: ACID compliance for data integrity
- **Row Locking**: Prevents race conditions with FOR UPDATE
- **Atomic Operations**: All-or-nothing booking process
- **Reference Generation**: Unique booking identifiers
- **Inventory Management**: Real-time ticket count updates

## 7. Admin Panel

### 7.1 Dashboard Statistics (admin/index.php)
```php
// Get total statistics
$stmt = $pdo->prepare("SELECT COUNT(*) as total_events FROM events WHERE status = 'active'");
$stmt->execute();
$total_events = $stmt->fetchColumn();

$stmt = $pdo->prepare("SELECT COUNT(*) as total_bookings FROM bookings WHERE status = 'confirmed'");
$stmt->execute();
$total_bookings = $stmt->fetchColumn();

$stmt = $pdo->prepare("SELECT SUM(total_amount) as total_revenue FROM bookings WHERE status = 'confirmed'");
$stmt->execute();
$total_revenue = $stmt->fetchColumn() ?: 0;

// Popular events
$stmt = $pdo->prepare("
    SELECT e.title, COUNT(b.id) as booking_count, SUM(b.total_amount) as revenue
    FROM events e
    LEFT JOIN bookings b ON e.id = b.event_id AND b.status = 'confirmed'
    WHERE e.status = 'active'
    GROUP BY e.id, e.title
    ORDER BY booking_count DESC
    LIMIT 5
");
$stmt->execute();
$popular_events = $stmt->fetchAll();
```

**Key Features:**
- **Aggregate Functions**: Statistical data computation
- **LEFT JOIN**: Includes events without bookings
- **GROUP BY**: Data aggregation by event
- **Performance Metrics**: Key business indicators

## 8. Security Implementation

### 8.1 Input Sanitization
```php
function sanitizeInput($data) {
    $data = trim($data);           // Remove whitespace
    $data = stripslashes($data);   // Remove backslashes
    $data = htmlspecialchars($data); // Convert special characters
    return $data;
}
```

### 8.2 Access Control
```php
function requireAdmin() {
    if (!isLoggedIn()) {
        if (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) {
            redirect('../adminlogin.php');
        } else {
            redirect('adminlogin.php');
        }
    } elseif (!isAdmin()) {
        redirect('../index.php');
    }
}
```

**Key Features:**
- **XSS Prevention**: HTML entity encoding
- **Role-based Access**: Admin-only areas protection
- **Path-aware Redirects**: Context-sensitive navigation

This code explanation provides detailed insights into EventTopia's implementation, highlighting key programming concepts, security measures, and architectural decisions.
