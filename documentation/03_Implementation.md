# EventTopia - Implementation Documentation

## 1. Development Environment Setup

### 1.1 Technology Stack
- **Operating System**: Linux (Ubuntu/Debian recommended)
- **Web Server**: Apache 2.4+
- **Database**: MySQL 8.0+
- **Programming Language**: PHP 8.0+
- **Frontend Framework**: Bootstrap 5
- **JavaScript Library**: jQuery 3.6+

### 1.2 LAMP Stack Installation
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Apache
sudo apt install apache2 -y

# Install MySQL
sudo apt install mysql-server -y

# Install PHP and extensions
sudo apt install php php-mysql php-cli php-curl php-json php-mbstring -y

# Enable Apache modules
sudo a2enmod rewrite
sudo systemctl restart apache2
```

### 1.3 Project Structure
```
EventTopia/
├── config/                 # Configuration files
│   ├── config.php         # Application configuration
│   └── database.php       # Database connection class
├── includes/              # Common includes
│   ├── header.php         # HTML header template
│   ├── footer.php         # HTML footer template
│   ├── navbar.php         # Navigation bar
│   └── pdf_ticket.php     # PDF ticket generation
├── assets/                # Static assets
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   └── images/           # Image uploads
├── ajax/                  # AJAX endpoints
│   ├── add_to_cart.php   # Cart management
│   ├── update_cart.php   # Cart updates
│   └── remove_from_cart.php
├── admin/                 # Admin panel
│   ├── index.php         # Admin dashboard
│   ├── events.php        # Event management
│   ├── add_event.php     # Add new events
│   ├── bookings.php      # Booking management
│   ├── users.php         # User management
│   └── reports.php       # Report generation
├── user/                  # User dashboard
│   ├── dashboard.php     # User dashboard
│   ├── profile.php       # Profile management
│   └── booking_history.php
└── documentation/         # Project documentation
```

## 2. Core Implementation Details

### 2.1 Database Connection (config/database.php)
```php
class Database {
    private $host = 'localhost';
    private $db_name = 'eventtopia';
    private $username = 'eventtopia_user';
    private $password = 'secure_password';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION)
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        return $this->conn;
    }
}
```

### 2.2 Authentication System
```php
// User Registration
function registerUser($username, $email, $password, $first_name, $last_name) {
    global $pdo;
    
    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password, first_name, last_name) 
        VALUES (?, ?, ?, ?, ?)
    ");
    
    return $stmt->execute([$username, $email, $hashed_password, $first_name, $last_name]);
}

// User Login
function loginUser($email, $password) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['role'] = $user['role'];
        return true;
    }
    return false;
}
```

### 2.3 Event Management System
```php
// Create Event
function createEvent($data) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        INSERT INTO events (title, description, event_date, event_time, venue, 
                          location, organizer, organizer_contact, price, 
                          available_tickets, total_tickets, category, image) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    return $stmt->execute([
        $data['title'], $data['description'], $data['event_date'],
        $data['event_time'], $data['venue'], $data['location'],
        $data['organizer'], $data['organizer_contact'], $data['price'],
        $data['available_tickets'], $data['total_tickets'],
        $data['category'], $data['image']
    ]);
}

// Get Active Events
function getActiveEvents($limit = null, $offset = 0) {
    global $pdo;
    
    $sql = "SELECT * FROM events WHERE status = 'active' AND event_date >= CURDATE() 
            ORDER BY event_date ASC";
    
    if ($limit) {
        $sql .= " LIMIT $limit OFFSET $offset";
    }
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}
```

### 2.4 Shopping Cart Implementation
```php
// Add to Cart (AJAX)
function addToCart($user_id, $event_id, $quantity) {
    global $pdo;
    
    // Check if item already exists
    $stmt = $pdo->prepare("SELECT * FROM cart WHERE user_id = ? AND event_id = ?");
    $stmt->execute([$user_id, $event_id]);
    
    if ($stmt->fetch()) {
        // Update quantity
        $stmt = $pdo->prepare("UPDATE cart SET quantity = quantity + ? WHERE user_id = ? AND event_id = ?");
        return $stmt->execute([$quantity, $user_id, $event_id]);
    } else {
        // Insert new item
        $stmt = $pdo->prepare("INSERT INTO cart (user_id, event_id, quantity) VALUES (?, ?, ?)");
        return $stmt->execute([$user_id, $event_id, $quantity]);
    }
}

// Get Cart Items
function getCartItems($user_id) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT c.*, e.title, e.event_date, e.event_time, e.venue, e.price, e.available_tickets
        FROM cart c
        JOIN events e ON c.event_id = e.id
        WHERE c.user_id = ? AND e.status = 'active'
        ORDER BY c.added_at DESC
    ");
    
    $stmt->execute([$user_id]);
    return $stmt->fetchAll();
}
```

### 2.5 Booking System
```php
// Process Booking
function processBooking($user_id, $cart_items, $attendee_data) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        foreach ($cart_items as $item) {
            // Check ticket availability
            $stmt = $pdo->prepare("SELECT available_tickets FROM events WHERE id = ?");
            $stmt->execute([$item['event_id']]);
            $available = $stmt->fetchColumn();
            
            if ($available < $item['quantity']) {
                throw new Exception("Not enough tickets available for " . $item['title']);
            }
            
            // Create booking
            $booking_reference = generateBookingReference();
            $booking_total = $item['price'] * $item['quantity'];
            
            $stmt = $pdo->prepare("
                INSERT INTO bookings (user_id, event_id, quantity, total_amount, 
                                    booking_reference, attendee_name, attendee_email, 
                                    attendee_phone, status, payment_status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'confirmed', 'completed')
            ");
            
            $stmt->execute([
                $user_id, $item['event_id'], $item['quantity'], $booking_total,
                $booking_reference, $attendee_data['name'], $attendee_data['email'],
                $attendee_data['phone']
            ]);
            
            // Update available tickets
            $stmt = $pdo->prepare("UPDATE events SET available_tickets = available_tickets - ? WHERE id = ?");
            $stmt->execute([$item['quantity'], $item['event_id']]);
        }
        
        // Clear cart
        $stmt = $pdo->prepare("DELETE FROM cart WHERE user_id = ?");
        $stmt->execute([$user_id]);
        
        $pdo->commit();
        return true;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}
```

## 3. Security Implementation

### 3.1 Input Validation and Sanitization
```php
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validateDate($date) {
    $d = DateTime::createFromFormat('Y-m-d', $date);
    return $d && $d->format('Y-m-d') === $date;
}
```

### 3.2 SQL Injection Prevention
- All database queries use PDO prepared statements
- No direct SQL string concatenation
- Parameter binding for all user inputs

### 3.3 XSS Protection
- All output is escaped using `htmlspecialchars()`
- Input sanitization on all form submissions
- Content Security Policy headers (recommended)

### 3.4 Session Security
```php
// Secure session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);

// Session regeneration on login
session_regenerate_id(true);
```

## 4. Frontend Implementation

### 4.1 Responsive Design
- Bootstrap 5 framework for responsive grid system
- Mobile-first approach
- Flexible layouts for all screen sizes

### 4.2 AJAX Implementation
```javascript
// Add to Cart AJAX
function addToCart(eventId, quantity) {
    $.ajax({
        url: 'ajax/add_to_cart.php',
        method: 'POST',
        data: {
            event_id: eventId,
            quantity: quantity
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                updateCartCount();
                showAlert('Item added to cart!', 'success');
            } else {
                showAlert(response.message, 'error');
            }
        },
        error: function() {
            showAlert('An error occurred. Please try again.', 'error');
        }
    });
}

// Update cart count
function updateCartCount() {
    $.get('ajax/get_cart_count.php', function(data) {
        $('#cart-count').text(data.count);
    });
}
```

### 4.3 Form Validation
```javascript
// Client-side validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    const inputs = form.querySelectorAll('input[required], select[required]');
    
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            showFieldError(input, 'This field is required');
            isValid = false;
        }
    });
    
    return isValid;
}
```

## 5. Performance Optimization

### 5.1 Database Optimization
- Proper indexing on frequently queried columns
- Pagination for large result sets
- Optimized queries with appropriate JOINs

### 5.2 Caching Strategy
- Browser caching for static assets
- Database query optimization
- Image compression and optimization

### 5.3 Code Organization
- Modular code structure
- Separation of concerns
- Reusable functions and components

This implementation documentation provides detailed insights into the technical aspects of EventTopia's development, covering database design, security measures, and performance considerations.
